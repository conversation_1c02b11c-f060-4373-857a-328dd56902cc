@import "tailwindcss";
/* import Inter font family from Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");
/* import Roboto font family from Google Fonts */
@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700&display=swap");

/* Custom styles */
* {
  box-sizing: border-box;
  -webkit-font-smoothing: inherit;
}

body {
  font-family: sans-serif;
  font-size: 12px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@layer base {
  nav.nav {
    padding: 24px;
    padding-left: 72px;
  }
  img.nav-logo {
    height: 20px;
    width: 20px;
    max-width: unset;
    border-radius: inherit;
    object-position: center;
    object-fit: cover;
  }
  p.link-label {
    --extracted-r6o4lv: rgb(31, 31, 31);
    --variable-reference-RdtYs86VA-x49uZyRwq: rgb(31, 31, 31);
    --framer-text-color: var(
      --extracted-r6o4lv,
      var(--variable-reference-RdtYs86VA-x49uZyRwq)
    );
    --framer-font-family: "Roboto", "Roboto Placeholder", sans-serif;
    --framer-font-family-bold: "Roboto", "Roboto Placeholder", sans-serif;
    --framer-font-family-bold-italic:
      "Roboto", "Roboto Placeholder", sans-serif;
    --framer-font-family-italic: "Roboto", "Roboto Placeholder", sans-serif;
    --framer-font-open-type-features: normal;
    --framer-font-size: 14px;
    --framer-font-style: normal;
    --framer-font-style-bold: normal;
    --framer-font-style-bold-italic: italic;
    --framer-font-style-italic: italic;
    --framer-font-variation-axes: normal;
    --framer-font-weight: 500;
    --framer-font-weight-bold: 700;
    --framer-font-weight-bold-italic: 700;
    --framer-font-weight-italic: 500;
    --framer-letter-spacing: 0em;
    --framer-line-height: 20px;
    --framer-paragraph-spacing: 0px;
    --framer-text-alignment: center;
    --framer-text-decoration: none;
    --framer-text-stroke-color: initial;
    --framer-text-stroke-width: initial;
    --framer-text-transform: none;
    font-family: var(
      --framer-blockquote-font-family,
      var(--framer-font-family, Inter, Inter Placeholder, sans-serif)
    );
    font-style: var(
      --framer-blockquote-font-style,
      var(--framer-font-style, normal)
    );
    font-weight: var(
      --framer-blockquote-font-weight,
      var(--framer-font-weight, 400)
    );
    color: var(--framer-blockquote-text-color, var(--framer-text-color, #000));
    font-size: calc(
      var(--framer-blockquote-font-size, var(--framer-font-size, 16px)) *
        var(--framer-font-size-scale, 1)
    );
    letter-spacing: var(
      --framer-blockquote-letter-spacing,
      var(--framer-letter-spacing, 0)
    );
    text-transform: var(
      --framer-blockquote-text-transform,
      var(--framer-text-transform, none)
    );
    text-decoration: var(
      --framer-blockquote-text-decoration,
      var(--framer-text-decoration, none)
    );
    line-height: var(
      --framer-blockquote-line-height,
      var(--framer-line-height, 1.2em)
    );
    text-align: var(
      --framer-blockquote-text-alignment,
      var(--framer-text-alignment, start)
    );
    -webkit-text-stroke-width: var(--framer-text-stroke-width, initial);
    -webkit-text-stroke-color: var(--framer-text-stroke-color, initial);
    font-feature-settings: var(--framer-font-open-type-features, initial);
    font-variation-settings: var(--framer-font-variation-axes, normal);
  }
}
