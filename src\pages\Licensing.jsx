import Hero from '../components/common/Hero';
import Card from '../components/ui/Card';

const Licensing = () => {
  const licenses = [
    {
      name: 'Standard License',
      price: '$2,999',
      description: 'Perfect for small to medium businesses',
      features: [
        'Single domain usage',
        'Basic customization',
        '1 year of updates',
        'Email support',
        'Documentation included'
      ],
      popular: false
    },
    {
      name: 'Professional License',
      price: '$5,999',
      description: 'Ideal for growing businesses and agencies',
      features: [
        'Up to 5 domains',
        'Advanced customization',
        '2 years of updates',
        'Priority support',
        'Source code access',
        'White-label options'
      ],
      popular: true
    },
    {
      name: 'Enterprise License',
      price: 'Custom',
      description: 'For large organizations with specific needs',
      features: [
        'Unlimited domains',
        'Full customization',
        'Lifetime updates',
        '24/7 dedicated support',
        'Complete source code',
        'Custom development',
        'Training included'
      ],
      popular: false
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <Hero
        title="Licensing Information"
        description="Choose the right license for your project. All our solutions come with comprehensive licensing options to meet your business needs."
      />

      {/* Licensing Options */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Choose Your License
            </h2>
            <p className="mt-4 text-xl text-gray-600">
              Flexible licensing options designed to grow with your business
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {licenses.map((license, index) => (
              <Card 
                key={index} 
                className={`relative text-center ${license.popular ? 'ring-2 ring-gray-900' : ''}`}
              >
                {license.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-gray-900 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {license.name}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {license.description}
                  </p>
                  <div className="text-4xl font-bold text-gray-900">
                    {license.price}
                  </div>
                </div>

                <ul className="space-y-3 mb-8">
                  {license.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <span className="text-green-500 mr-3">✓</span>
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button className={`w-full py-3 px-6 rounded-md font-medium transition-colors ${
                  license.popular 
                    ? 'bg-gray-900 text-white hover:bg-gray-800' 
                    : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}>
                  {license.price === 'Custom' ? 'Contact Sales' : 'Get Started'}
                </button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* License Terms */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-4xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              License Terms & Conditions
            </h2>
          </div>

          <div className="space-y-8">
            <Card>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Usage Rights</h3>
              <p className="text-gray-600 mb-4">
                Our licenses grant you the right to use our software and solutions according to the 
                terms specified in your license agreement. Usage rights vary by license type.
              </p>
              <ul className="list-disc pl-6 text-gray-600 space-y-2">
                <li>Commercial use permitted for all license types</li>
                <li>Modification rights included with Professional and Enterprise licenses</li>
                <li>Redistribution requires explicit permission</li>
                <li>Reverse engineering is prohibited</li>
              </ul>
            </Card>

            <Card>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Support & Updates</h3>
              <p className="text-gray-600 mb-4">
                All licenses include support and updates for the specified duration:
              </p>
              <ul className="list-disc pl-6 text-gray-600 space-y-2">
                <li>Bug fixes and security updates included</li>
                <li>Feature updates available during support period</li>
                <li>Extended support available for purchase</li>
                <li>Migration assistance for major version updates</li>
              </ul>
            </Card>

            <Card>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Restrictions</h3>
              <p className="text-gray-600 mb-4">
                The following restrictions apply to all license types:
              </p>
              <ul className="list-disc pl-6 text-gray-600 space-y-2">
                <li>Cannot remove or modify copyright notices</li>
                <li>Cannot use our trademarks without permission</li>
                <li>Cannot sublicense or transfer without approval</li>
                <li>Must comply with applicable laws and regulations</li>
              </ul>
            </Card>

            <Card>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Warranty & Liability</h3>
              <p className="text-gray-600">
                Our software is provided "as is" without warranty of any kind. We provide reasonable 
                efforts to ensure quality and functionality, but cannot guarantee error-free operation. 
                Liability is limited to the amount paid for the license.
              </p>
            </Card>

            <Card>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Third-Party Components</h3>
              <p className="text-gray-600">
                Our solutions may include third-party components subject to their own licenses. 
                A complete list of third-party components and their licenses is provided with 
                each product. You are responsible for complying with all applicable third-party licenses.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Need a Custom License?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Have specific requirements that don't fit our standard licenses? 
            Contact our sales team to discuss custom licensing options.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gray-900 text-white px-8 py-3 rounded-md font-medium hover:bg-gray-800 transition-colors">
              Contact Sales
            </button>
            <button className="border border-gray-300 text-gray-700 px-8 py-3 rounded-md font-medium hover:bg-gray-50 transition-colors">
              View Documentation
            </button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Licensing;
