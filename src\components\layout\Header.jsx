import { useState } from "react";
import { Link, useLocation } from "react-router-dom";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: "Home", href: "/" },
    { name: "About", href: "/about" },
    { name: "Cases", href: "/cases" },
    { name: "Blog", href: "/blog" },
    { name: "Contact", href: "/contact" },
  ];

  const isActive = (path) => location.pathname === path;
  const [hoveredItem, setHoveredItem] = useState(null);

  return (
    <header>
      <nav className="nav sticky top-0 z-10 flex h-[84px] w-full max-w-[1200px] flex-none flex-row flex-nowrap items-center justify-center gap-0 overflow-hidden will-change-transform">
        <div className="relative z-10 size-auto flex-none">
          {/* Desktop Navigation */}
          <nav className="relative hidden min-xl:flex items-center justify-center w-full">
            <div className="flex h-min w-full max-w-[1200px] flex-none flex-row items-center justify-between gap-8 p-0">
              {/* Logo on the left */}
              <div className="flex items-center">
                <a
                  className="flex items-center gap-0 rounded-[8px] p-2"
                  href="/"
                >
                  <div className="size-5">
                    <img
                      className="nav-logo"
                      src="/images/0R0UXrjNeRBEKk7xpEnnM93qA.png"
                      alt=""
                    />
                  </div>
                </a>
              </div>
              
              {/* Navigation links on the right */}
              <div className="flex items-center gap-1">
                {navigation.map((item) => (
                  <div
                    key={item.href}
                    className="relative flex-none"
                    onMouseEnter={() => setHoveredItem(item.href)}
                    onMouseLeave={() => setHoveredItem(null)}
                  >
                    <Link
                      to={item.href}
                      className={`relative flex items-center justify-start gap-2.5 px-4 py-2 decoration-0 ${isActive(item.href) ? "cursor-default" : "cursor-pointer"}`}
                    >
                      {(isActive(item.href) || hoveredItem === item.href) && (
                        <div className="absolute right-0 bottom-[5px] left-0 z-10 h-[3px] flex-none">
                          <div className="absolute left-1/2 w-[3px] h-[3px] -translate-x-1/2 rounded-full bg-[rgb(31,31,31)]" />
                        </div>
                      )}
                      <div className="relative flex flex-col justify-start">
                        <p className="link-label">{item.name}</p>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </nav>
        </div>
      </nav>
    </header>
  );
};

export default Header;
