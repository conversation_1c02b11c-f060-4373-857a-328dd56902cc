import Hero from '../components/common/Hero';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const Blog = () => {
  const blogPosts = [
    {
      title: 'The Future of Web Development: Trends to Watch in 2024',
      excerpt: 'Explore the latest trends shaping the web development landscape, from AI integration to progressive web apps.',
      author: '<PERSON>',
      date: 'March 15, 2024',
      category: 'Technology',
      readTime: '5 min read',
      image: '💻'
    },
    {
      title: 'Building Scalable E-commerce Solutions: Best Practices',
      excerpt: 'Learn how to create e-commerce platforms that can handle growth and provide excellent user experiences.',
      author: '<PERSON>',
      date: 'March 10, 2024',
      category: 'E-commerce',
      readTime: '8 min read',
      image: '🛍️'
    },
    {
      title: 'Digital Transformation: A Step-by-Step Guide for SMBs',
      excerpt: 'Practical advice for small and medium businesses looking to embrace digital transformation.',
      author: '<PERSON>',
      date: 'March 5, 2024',
      category: 'Business',
      readTime: '6 min read',
      image: '🚀'
    },
    {
      title: 'UX Design Principles That Drive Conversions',
      excerpt: 'Discover how thoughtful UX design can significantly impact your business metrics and user satisfaction.',
      author: '<PERSON>',
      date: 'February 28, 2024',
      category: 'Design',
      readTime: '7 min read',
      image: '🎨'
    },
    {
      title: 'Cloud Migration Strategies for Modern Businesses',
      excerpt: 'Everything you need to know about moving your infrastructure to the cloud safely and efficiently.',
      author: 'David Kim',
      date: 'February 20, 2024',
      category: 'Cloud',
      readTime: '10 min read',
      image: '☁️'
    },
    {
      title: 'AI and Machine Learning in Business Applications',
      excerpt: 'How artificial intelligence is revolutionizing business processes and creating new opportunities.',
      author: 'Michael Rodriguez',
      date: 'February 15, 2024',
      category: 'AI/ML',
      readTime: '9 min read',
      image: '🤖'
    }
  ];

  const categories = ['All', 'Technology', 'Business', 'Design', 'E-commerce', 'Cloud', 'AI/ML'];
  const featuredPost = blogPosts[0];
  const regularPosts = blogPosts.slice(1);

  return (
    <div>
      {/* Hero Section */}
      <Hero
        title="Our Blog"
        description="Insights, tips, and industry knowledge from our team of experts. Stay updated with the latest trends and best practices."
      />

      {/* Featured Post */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Featured Article</h2>
          </div>
          
          <Card className="overflow-hidden">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="flex items-center justify-center bg-gray-50 p-12">
                <div className="text-8xl">{featuredPost.image}</div>
              </div>
              <div className="p-8 lg:p-12">
                <div className="flex items-center gap-4 mb-4">
                  <span className="px-3 py-1 text-xs font-semibold text-gray-600 bg-gray-100 rounded-full">
                    {featuredPost.category}
                  </span>
                  <span className="text-sm text-gray-500">{featuredPost.readTime}</span>
                </div>
                
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {featuredPost.title}
                </h3>
                
                <p className="text-gray-600 mb-6">
                  {featuredPost.excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">👤</div>
                    <div>
                      <p className="font-medium text-gray-900">{featuredPost.author}</p>
                      <p className="text-sm text-gray-500">{featuredPost.date}</p>
                    </div>
                  </div>
                  
                  <Button>Read More</Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <Button
                key={category}
                variant={category === 'All' ? 'primary' : 'outline'}
                size="sm"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {regularPosts.map((post, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <div className="text-center mb-6">
                  <div className="text-6xl mb-4">{post.image}</div>
                  <div className="flex items-center justify-center gap-4 mb-4">
                    <span className="px-3 py-1 text-xs font-semibold text-gray-600 bg-gray-100 rounded-full">
                      {post.category}
                    </span>
                    <span className="text-sm text-gray-500">{post.readTime}</span>
                  </div>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {post.title}
                </h3>
                
                <p className="text-gray-600 mb-6">
                  {post.excerpt}
                </p>
                
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-2">
                    <div className="text-lg">👤</div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{post.author}</p>
                      <p className="text-xs text-gray-500">{post.date}</p>
                    </div>
                  </div>
                </div>
                
                <Button variant="outline" className="w-full">
                  Read Article
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Subscribe to our newsletter and get the latest insights delivered to your inbox.
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="Enter your email"
              className="flex-1 px-4 py-2 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-white"
            />
            <Button className="bg-white text-gray-900 hover:bg-gray-100">
              Subscribe
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Blog;
