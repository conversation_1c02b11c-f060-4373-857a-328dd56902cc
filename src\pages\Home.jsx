import { Link } from 'react-router-dom';
import Hero from '../components/common/Hero';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const Home = () => {
  const features = [
    {
      icon: '🚀',
      title: 'Innovation',
      description: 'Cutting-edge solutions that drive your business forward with the latest technologies and methodologies.'
    },
    {
      icon: '🤝',
      title: 'Partnership',
      description: 'We work closely with you as a trusted partner, understanding your unique needs and challenges.'
    },
    {
      icon: '📈',
      title: 'Growth',
      description: 'Scalable strategies designed to help your business grow sustainably and reach new heights.'
    },
    {
      icon: '🎯',
      title: 'Results',
      description: 'Data-driven approaches that deliver measurable results and tangible business value.'
    }
  ];

  const stats = [
    { number: '100+', label: 'Happy Clients' },
    { number: '500+', label: 'Projects Completed' },
    { number: '50+', label: 'Team Members' },
    { number: '5+', label: 'Years Experience' }
  ];

  return (
    <div>
      {/* Hero Section */}
      <Hero
        subtitle="Welcome to"
        title="Panda Patronage"
        description="Empowering businesses through innovative solutions and strategic partnerships. We help organizations grow and thrive in the digital age."
        primaryAction={{
          text: "Get Started",
          onClick: () => window.location.href = '/contact'
        }}
        secondaryAction={{
          text: "Learn More",
          onClick: () => window.location.href = '/about'
        }}
      />

      {/* Features Section */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Why Choose Panda Patronage?
            </h2>
            <p className="mt-4 text-xl text-gray-600 max-w-3xl mx-auto">
              We combine expertise, innovation, and dedication to deliver exceptional results for our clients.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-300">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl mb-8">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Let's discuss how we can help your business grow and succeed in today's competitive landscape.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <Button size="lg">
                  Contact Us Today
                </Button>
              </Link>
              <Link to="/cases">
                <Button variant="outline" size="lg">
                  View Our Work
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
