import Hero from '../components/common/Hero';
import ContactForm from '../components/common/ContactForm';
import Card from '../components/ui/Card';

const Contact = () => {
  const contactInfo = [
    {
      icon: '📧',
      title: 'Email',
      details: '<EMAIL>',
      description: 'Send us an email anytime'
    },
    {
      icon: '📞',
      title: 'Phone',
      details: '+****************',
      description: 'Mon-Fri from 8am to 6pm'
    },
    {
      icon: '📍',
      title: 'Office',
      details: '123 Business Ave, Suite 100',
      description: 'San Francisco, CA 94105'
    },
    {
      icon: '💬',
      title: 'Live Chat',
      details: 'Available 24/7',
      description: 'Get instant support'
    }
  ];

  const offices = [
    {
      city: 'San Francisco',
      address: '123 Business Ave, Suite 100\nSan Francisco, CA 94105',
      phone: '+****************',
      email: '<EMAIL>'
    },
    {
      city: 'New York',
      address: '456 Tech Street, Floor 15\nNew York, NY 10001',
      phone: '+****************',
      email: '<EMAIL>'
    },
    {
      city: 'London',
      address: '789 Innovation Road\nLondon, UK EC1A 1BB',
      phone: '+44 20 7123 4567',
      email: '<EMAIL>'
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <Hero
        title="Get in Touch"
        description="Ready to start your next project? We'd love to hear from you. Send us a message and we'll respond as soon as possible."
      />

      {/* Contact Info Cards */}
      <section className="py-16 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">{info.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {info.title}
                </h3>
                <p className="text-gray-900 font-medium mb-1">
                  {info.details}
                </p>
                <p className="text-gray-600 text-sm">
                  {info.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Send Us a Message
            </h2>
            <p className="mt-4 text-xl text-gray-600">
              Fill out the form below and we'll get back to you within 24 hours.
            </p>
          </div>

          <ContactForm />
        </div>
      </section>

      {/* Office Locations */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Our Offices
            </h2>
            <p className="mt-4 text-xl text-gray-600">
              Visit us at one of our locations around the world.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {offices.map((office, index) => (
              <Card key={index} className="text-center">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {office.city}
                </h3>
                <div className="space-y-3 text-gray-600">
                  <div>
                    <div className="text-2xl mb-2">📍</div>
                    <p className="whitespace-pre-line">{office.address}</p>
                  </div>
                  <div>
                    <div className="text-2xl mb-2">📞</div>
                    <p>{office.phone}</p>
                  </div>
                  <div>
                    <div className="text-2xl mb-2">📧</div>
                    <p>{office.email}</p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="max-w-3xl mx-auto space-y-8">
            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                How long does a typical project take?
              </h3>
              <p className="text-gray-600">
                Project timelines vary depending on scope and complexity. Most projects range from 
                4-12 weeks, but we'll provide a detailed timeline during our initial consultation.
              </p>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Do you work with startups?
              </h3>
              <p className="text-gray-600">
                Absolutely! We love working with startups and have special packages designed to 
                help early-stage companies get off the ground with limited budgets.
              </p>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                What technologies do you specialize in?
              </h3>
              <p className="text-gray-600">
                We specialize in modern web technologies including React, Node.js, Python, and 
                cloud platforms like AWS and Azure. We always choose the best technology for your specific needs.
              </p>
            </Card>

            <Card>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                Do you provide ongoing support?
              </h3>
              <p className="text-gray-600">
                Yes, we offer various support and maintenance packages to ensure your solution 
                continues to perform optimally after launch.
              </p>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
