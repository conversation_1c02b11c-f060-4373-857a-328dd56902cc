import Button from '../ui/Button';

const Hero = ({ 
  title, 
  subtitle, 
  description, 
  primaryAction, 
  secondaryAction,
  backgroundImage,
  className = '' 
}) => {
  return (
    <div className={`relative bg-gray-900 ${className}`}>
      {/* Background Image */}
      {backgroundImage && (
        <div className="absolute inset-0">
          <img
            className="w-full h-full object-cover opacity-30"
            src={backgroundImage}
            alt=""
          />
        </div>
      )}
      
      {/* Content */}
      <div className="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-24 sm:py-32">
        <div className="text-center">
          {subtitle && (
            <p className="text-base font-semibold text-gray-300 uppercase tracking-wide">
              {subtitle}
            </p>
          )}
          
          <h1 className="mt-4 text-4xl font-bold tracking-tight text-white sm:text-5xl lg:text-6xl">
            {title}
          </h1>
          
          {description && (
            <p className="mt-6 max-w-3xl mx-auto text-xl text-gray-300">
              {description}
            </p>
          )}
          
          {(primaryAction || secondaryAction) && (
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
              {primaryAction && (
                <Button
                  size="lg"
                  onClick={primaryAction.onClick}
                  className="bg-white text-gray-900 hover:bg-gray-100"
                >
                  {primaryAction.text}
                </Button>
              )}
              
              {secondaryAction && (
                <Button
                  variant="outline"
                  size="lg"
                  onClick={secondaryAction.onClick}
                  className="border-white text-white hover:bg-white hover:text-gray-900"
                >
                  {secondaryAction.text}
                </Button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Hero;
