import Hero from '../components/common/Hero';
import Card from '../components/ui/Card';

const About = () => {
  const team = [
    {
      name: '<PERSON>',
      role: 'CEO & Founder',
      image: '👩‍💼',
      bio: 'Visionary leader with 15+ years in business strategy and digital transformation.'
    },
    {
      name: '<PERSON>',
      role: 'CTO',
      image: '👨‍💻',
      bio: 'Technology expert specializing in scalable solutions and innovative architectures.'
    },
    {
      name: '<PERSON>',
      role: 'Head of Design',
      image: '👩‍🎨',
      bio: 'Creative director passionate about user experience and beautiful, functional design.'
    },
    {
      name: '<PERSON>',
      role: 'Lead Developer',
      image: '👨‍🔬',
      bio: 'Full-stack developer with expertise in modern web technologies and best practices.'
    }
  ];

  const values = [
    {
      title: 'Innovation',
      description: 'We constantly push boundaries and explore new technologies to deliver cutting-edge solutions.',
      icon: '💡'
    },
    {
      title: 'Integrity',
      description: 'We build trust through transparency, honesty, and ethical business practices.',
      icon: '🤝'
    },
    {
      title: 'Excellence',
      description: 'We strive for perfection in everything we do, delivering quality that exceeds expectations.',
      icon: '⭐'
    },
    {
      title: 'Collaboration',
      description: 'We believe in the power of teamwork and partnership to achieve extraordinary results.',
      icon: '🤝'
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <Hero
        title="About Panda Patronage"
        description="We are a team of passionate professionals dedicated to helping businesses thrive through innovative solutions and strategic partnerships."
      />

      {/* Story Section */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-600">
                <p>
                  Founded in 2019, Panda Patronage began with a simple mission: to bridge the gap between 
                  innovative technology and practical business solutions. Our founders recognized that many 
                  businesses struggled to leverage new technologies effectively.
                </p>
                <p>
                  Today, we've grown into a trusted partner for organizations of all sizes, from startups 
                  to enterprise companies. Our team combines deep technical expertise with business acumen 
                  to deliver solutions that drive real results.
                </p>
                <p>
                  We believe that every business has unique challenges and opportunities. That's why we 
                  take a personalized approach to every project, working closely with our clients to 
                  understand their goals and develop tailored strategies.
                </p>
              </div>
            </div>
            <div className="text-center">
              <div className="text-8xl mb-4">🐼</div>
              <p className="text-gray-600 italic">
                "Empowering businesses, one partnership at a time."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">Our Values</h2>
            <p className="mt-4 text-xl text-gray-600">
              The principles that guide everything we do
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center">
                <div className="text-4xl mb-4">{value.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">
                  {value.title}
                </h3>
                <p className="text-gray-600">
                  {value.description}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold text-gray-900 sm:text-4xl">Meet Our Team</h2>
            <p className="mt-4 text-xl text-gray-600">
              The talented individuals behind our success
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center">
                <div className="text-6xl mb-4">{member.image}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-gray-600 font-medium mb-3">
                  {member.role}
                </p>
                <p className="text-gray-600 text-sm">
                  {member.bio}
                </p>
              </Card>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
