# 📸 Image Management Guide for Panda Patronage

## 📁 Folder Structure

```
panda-patronage/
├── src/assets/images/          # Images imported in components
│   ├── logos/                  # Company logos, brand assets
│   ├── team/                   # Team member photos
│   ├── projects/               # Project screenshots, case study images
│   └── icons/                  # Custom icons, illustrations
│
└── public/images/              # Static images accessed via URL
    ├── hero/                   # Hero section backgrounds
    ├── backgrounds/            # Page backgrounds, patterns
    └── meta/                   # Favicons, social media images
```

## 🎯 When to Use Each Location

### `src/assets/images/` - Component Images
**Use for:**
- Company logos
- Team member photos
- Project screenshots
- Icons and illustrations
- Images that need optimization/processing

**How to use:**
```jsx
// Import the image
import logo from '../assets/images/logos/panda-logo.png';
import teamPhoto from '../assets/images/team/sarah-chen.jpg';

// Use in component
<img src={logo} alt="Panda Patronage Logo" />
<img src={teamPhoto} alt="<PERSON>, CEO" />
```

**Benefits:**
- ✅ Vite optimizes and bundles these images
- ✅ Gets unique filenames for caching
- ✅ Build process will warn if image is missing
- ✅ Can be imported as modules

### `public/images/` - Static Images
**Use for:**
- Large background images
- Images referenced in CSS
- Favicons and meta images
- Images that don't need processing

**How to use:**
```jsx
// Reference directly by path (no import needed)
<img src="/images/hero/hero-background.jpg" alt="Hero Background" />

// In CSS or style props
<div style={{ backgroundImage: 'url(/images/backgrounds/pattern.svg)' }}>
```

**Benefits:**
- ✅ Served directly without processing
- ✅ Good for large images
- ✅ Can be referenced in HTML meta tags
- ✅ Accessible via direct URL

## 📋 Recommended Image Organization

### For Your Panda Patronage Website:

```
src/assets/images/
├── logos/
│   ├── panda-logo.svg          # Main logo
│   ├── panda-logo-white.svg    # White version for dark backgrounds
│   └── favicon.ico             # Favicon
├── team/
│   ├── sarah-chen.jpg          # CEO photo
│   ├── michael-rodriguez.jpg   # CTO photo
│   ├── emily-johnson.jpg       # Head of Design photo
│   └── david-kim.jpg           # Lead Developer photo
├── projects/
│   ├── ecommerce-platform.jpg  # Case study screenshots
│   ├── healthcare-system.jpg
│   ├── financial-dashboard.jpg
│   └── education-platform.jpg
└── icons/
    ├── innovation-icon.svg
    ├── partnership-icon.svg
    └── growth-icon.svg

public/images/
├── hero/
│   ├── hero-background.jpg     # Main hero background
│   └── about-hero.jpg          # About page hero
├── backgrounds/
│   ├── pattern.svg             # Decorative patterns
│   └── texture.jpg             # Background textures
└── meta/
    ├── og-image.jpg            # Open Graph image for social sharing
    └── favicon-32x32.png       # Various favicon sizes
```

## 🔧 Code Examples

### Example 1: Using Logo in Header Component
```jsx
// src/components/layout/Header.jsx
import logo from '../../assets/images/logos/panda-logo.svg';

const Header = () => {
  return (
    <header>
      <img src={logo} alt="Panda Patronage" className="h-8 w-auto" />
    </header>
  );
};
```

### Example 2: Team Photos in About Page
```jsx
// src/pages/About.jsx
import sarahPhoto from '../assets/images/team/sarah-chen.jpg';
import michaelPhoto from '../assets/images/team/michael-rodriguez.jpg';

const About = () => {
  const team = [
    { name: 'Sarah Chen', photo: sarahPhoto },
    { name: 'Michael Rodriguez', photo: michaelPhoto },
  ];

  return (
    <div>
      {team.map(member => (
        <img src={member.photo} alt={member.name} />
      ))}
    </div>
  );
};
```

### Example 3: Hero Background (Static)
```jsx
// src/components/common/Hero.jsx
const Hero = () => {
  return (
    <div 
      className="hero-section"
      style={{ 
        backgroundImage: 'url(/images/hero/hero-background.jpg)',
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <h1>Welcome to Panda Patronage</h1>
    </div>
  );
};
```

## 📐 Image Optimization Tips

1. **File Formats:**
   - Use `.svg` for logos and icons
   - Use `.jpg` for photos and complex images
   - Use `.png` for images with transparency
   - Use `.webp` for modern browsers (better compression)

2. **File Sizes:**
   - Logos: < 50KB
   - Team photos: < 200KB
   - Hero images: < 500KB
   - Thumbnails: < 100KB

3. **Naming Convention:**
   - Use kebab-case: `panda-logo.svg`
   - Be descriptive: `sarah-chen-ceo-photo.jpg`
   - Include dimensions if needed: `hero-1920x1080.jpg`

## 🚀 Quick Start

1. **Add your logo:**
   ```
   Place: src/assets/images/logos/panda-logo.svg
   Update: src/components/layout/Header.jsx
   ```

2. **Add team photos:**
   ```
   Place: src/assets/images/team/[name].jpg
   Update: src/pages/About.jsx
   ```

3. **Add hero background:**
   ```
   Place: public/images/hero/hero-background.jpg
   Update: src/components/common/Hero.jsx
   ```

This structure will keep your images organized and make your website load efficiently! 🐼
