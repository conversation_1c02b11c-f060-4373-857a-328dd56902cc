import Hero from '../components/common/Hero';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

const Cases = () => {
  const caseStudies = [
    {
      title: 'E-commerce Platform Transformation',
      client: 'TechRetail Inc.',
      category: 'E-commerce',
      description: 'Complete digital transformation of a traditional retail business, resulting in 300% increase in online sales.',
      image: '🛒',
      results: [
        '300% increase in online sales',
        '50% reduction in operational costs',
        '95% customer satisfaction rate'
      ],
      technologies: ['React', 'Node.js', 'AWS', 'Stripe']
    },
    {
      title: 'Healthcare Management System',
      client: 'MedCare Solutions',
      category: 'Healthcare',
      description: 'Development of a comprehensive patient management system that streamlined operations for a network of clinics.',
      image: '🏥',
      results: [
        '60% reduction in appointment scheduling time',
        '40% improvement in patient satisfaction',
        '25% increase in operational efficiency'
      ],
      technologies: ['Vue.js', 'Python', 'PostgreSQL', 'Docker']
    },
    {
      title: 'Financial Analytics Dashboard',
      client: 'InvestPro Analytics',
      category: 'Finance',
      description: 'Real-time financial analytics platform providing insights for investment decisions and portfolio management.',
      image: '📊',
      results: [
        '80% faster data processing',
        '99.9% system uptime',
        '35% improvement in decision-making speed'
      ],
      technologies: ['Angular', 'C#', 'SQL Server', 'Azure']
    },
    {
      title: 'Educational Learning Platform',
      client: 'EduTech Academy',
      category: 'Education',
      description: 'Interactive online learning platform with video streaming, assessments, and progress tracking.',
      image: '🎓',
      results: [
        '500% increase in student engagement',
        '90% course completion rate',
        '45% reduction in administrative workload'
      ],
      technologies: ['React', 'Express.js', 'MongoDB', 'WebRTC']
    },
    {
      title: 'Supply Chain Optimization',
      client: 'LogiFlow Corp',
      category: 'Logistics',
      description: 'AI-powered supply chain management system that optimized inventory and reduced delivery times.',
      image: '🚚',
      results: [
        '30% reduction in delivery times',
        '20% decrease in inventory costs',
        '99% order accuracy rate'
      ],
      technologies: ['Python', 'TensorFlow', 'Redis', 'Kubernetes']
    },
    {
      title: 'Smart City IoT Platform',
      client: 'CityTech Municipality',
      category: 'IoT',
      description: 'Comprehensive IoT platform for smart city infrastructure monitoring and management.',
      image: '🏙️',
      results: [
        '40% reduction in energy consumption',
        '50% improvement in traffic flow',
        '25% decrease in maintenance costs'
      ],
      technologies: ['IoT', 'Node.js', 'InfluxDB', 'Grafana']
    }
  ];

  const categories = ['All', 'E-commerce', 'Healthcare', 'Finance', 'Education', 'Logistics', 'IoT'];

  return (
    <div>
      {/* Hero Section */}
      <Hero
        title="Our Case Studies"
        description="Discover how we've helped businesses across various industries achieve their goals through innovative solutions and strategic partnerships."
      />

      {/* Filter Section */}
      <section className="py-12 bg-gray-50">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <Button
                key={category}
                variant={category === 'All' ? 'primary' : 'outline'}
                size="sm"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* Case Studies Grid */}
      <section className="py-24">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {caseStudies.map((caseStudy, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <div className="text-center mb-6">
                  <div className="text-6xl mb-4">{caseStudy.image}</div>
                  <span className="inline-block px-3 py-1 text-xs font-semibold text-gray-600 bg-gray-100 rounded-full">
                    {caseStudy.category}
                  </span>
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {caseStudy.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-1">
                  Client: {caseStudy.client}
                </p>
                
                <p className="text-gray-600 mb-4">
                  {caseStudy.description}
                </p>
                
                <div className="mb-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Key Results:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {caseStudy.results.map((result, resultIndex) => (
                      <li key={resultIndex} className="flex items-start">
                        <span className="text-green-500 mr-2">✓</span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
                
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-2">Technologies:</h4>
                  <div className="flex flex-wrap gap-2">
                    {caseStudy.technologies.map((tech, techIndex) => (
                      <span
                        key={techIndex}
                        className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
                
                <Button variant="outline" className="w-full">
                  View Details
                </Button>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gray-900">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Start Your Success Story?
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can help you achieve similar results for your business.
          </p>
          <Button size="lg" className="bg-white text-gray-900 hover:bg-gray-100">
            Get Started Today
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Cases;
